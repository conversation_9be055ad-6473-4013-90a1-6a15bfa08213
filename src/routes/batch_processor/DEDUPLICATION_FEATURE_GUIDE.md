# 查询去重功能使用指南

## 概述

现在用户可以选择是否对输入的查询进行去重处理。当用户有意输入多个相同的查询时，系统将不会自动去重，而是会处理所有的查询，包括重复的。

## 功能特性

### 1. 配置选项
在 `BatchConfig` 中新增了 `enableDeduplication` 选项：

```typescript
interface BatchConfig {
  processing: {
    // ... 其他配置
    enableDeduplication: boolean; // 启用查询去重（默认true，设为false时允许重复查询）
  };
}
```

### 2. 默认行为
- **默认启用去重**：`enableDeduplication: true`
- 保持向后兼容性，现有功能不受影响

### 3. 禁用去重
设置 `enableDeduplication: false` 时：
- 保留所有输入的查询，包括重复的
- 每个重复查询都会被单独处理
- 适用于用户有意提交相同查询的场景

## 使用方法

### 方法1：通过用户界面开关
在输入区域标题的右侧，有一个"去重"开关：
- ✅ 开关开启：启用去重（默认）
- ❌ 开关关闭：禁用去重，允许处理重复查询

### 方法2：通过配置文件
```typescript
const config: BatchConfig = {
  processing: {
    concurrent: 5,
    batchSize: 10,
    delayBetweenRequests: 2000,
    enableCache: true,
    skipExistingFiles: true,
    useInternalData: false,
    enableDeduplication: false, // 禁用去重
  },
  api: {
    // ... API配置
  }
};
```

### 方法3：通过服务更新配置
```typescript
// 获取批处理服务实例
const batchService = new EnhancedBatchProcessorService();

// 更新配置以禁用去重
batchService.updateConfig({
  processing: {
    enableDeduplication: false
  }
});
```

## 实际应用场景

### 场景1：测试相同查询的一致性
```
输入：
百数表
百数表
百数表

结果：
- enableDeduplication: true  → 处理1次
- enableDeduplication: false → 处理3次
```

### 场景2：批量生成相同内容的不同版本
```
输入：
九九乘法表
九九乘法表
九九乘法表

用途：生成多个版本的九九乘法表，比较AI输出的差异
```

## 技术实现

### 1. 查询解析层面
- `parseQueryList(text, enableDeduplication)` 函数支持可选去重
- 默认参数为 `true`，保持向后兼容

### 2. 批处理服务层面
- `EnhancedBatchProcessorService` 根据配置决定是否去重
- `deduplicateJobs()` 方法会检查配置决定是否执行
- `processQuery()` 方法会根据配置决定是否检查重复任务

### 3. 用户界面层面
- 页面输入解析会根据当前配置决定去重行为
- 配置更改会立即生效

## 注意事项

1. **性能考虑**：禁用去重可能导致更多的API调用和处理时间
2. **资源消耗**：重复查询会消耗更多的计算资源
3. **结果管理**：重复查询的结果需要用户自行管理和区分

## 配置建议

- **开发测试**：建议禁用去重，便于测试一致性
- **生产环境**：建议启用去重，节省资源
- **特殊需求**：根据具体业务场景灵活配置

## 使用示例

### 测试去重功能
1. 在输入框中输入以下内容：
```
百数表
三角函数
百数表
九九乘法表
三角函数
```

2. **启用去重时**（开关开启）：
   - 解析结果：4个查询（百数表、三角函数、九九乘法表、勾股定理）
   - 处理次数：每个查询处理1次

3. **禁用去重时**（开关关闭）：
   - 解析结果：5个查询（包含重复）
   - 处理次数：每个查询按输入次数处理

## 更新日志

- ✅ 新增 `enableDeduplication` 配置选项
- ✅ 更新 `parseQueryList` 函数支持可选去重
- ✅ 更新 `EnhancedBatchProcessorService` 支持配置化去重
- ✅ 更新页面输入解析逻辑
- ✅ 添加用户界面开关控制
- ✅ 添加工具提示说明功能
- ✅ 保持向后兼容性
