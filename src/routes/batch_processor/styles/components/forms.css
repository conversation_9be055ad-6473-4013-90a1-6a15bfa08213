/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 📝 UNIFIED FORM SYSTEM - 统一表单系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了design-system/forms.css + input-area-fix.css
 * 提供完整的表单组件解决方案
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces design-system/forms.css + input-area-fix.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 BASE FORM STYLES - 基础表单样式
 * ═══════════════════════════════════════════════════════════════════════════ */

.form {
  display: flex;
  flex-direction: column;
  gap: var(--form-gap-md);
}

/* 表单区块 - 优化间距 */
.form-section {
  margin-bottom: 20px;
}

.form-section:last-child {
  margin-bottom: 0;
}

/* 表单组 - 紧凑布局 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 14px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group.full-width {
  width: 100%;
}

/* 双列布局 - 优化间距 */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 14px;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* 数字输入卡片 */
.form-group.numeric-card {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(59, 130, 246, 0.08);
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.form-group.numeric-card:hover {
  border-color: rgba(59, 130, 246, 0.15);
  background: rgba(255, 255, 255, 0.7);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🏷️ FORM LABELS - 表单标签
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  cursor: pointer;
  transition: color 0.2s ease;
}

.form-label:hover {
  color: var(--color-primary-600);
}

.form-label.required::after {
  content: '*';
  color: var(--color-error-500);
  margin-left: var(--space-1);
}

/* 增强表单标签 */
.enhanced-form-label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-600);
  margin-bottom: var(--space-2);
  cursor: pointer;
  transition: color 0.2s ease;
}

.enhanced-form-label:hover {
  color: var(--color-primary-600);
}

.enhanced-form-label.required::after {
  content: '*';
  color: var(--color-error-500);
  margin-left: var(--space-1);
}

/* 字段提示 */
.field-hint {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 4px;
  opacity: 0.8;
}

/* 表单描述 */
.form-description {
  font-size: 0.8125rem;
  color: var(--text-secondary);
  margin-top: var(--space-1);
  line-height: 1.4;
}

/* 表单提示 */
.form-hint {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  margin-top: var(--space-1);
  font-style: italic;
}

/* 区块标题 - 优化视觉层次 */
.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  padding: 8px 0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary-500), var(--color-secondary-500));
  border-radius: 1px;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📝 INPUT FIELDS - 输入字段
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-input {
  width: 100%;
  padding: var(--input-padding-sm) var(--input-padding-md);
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-primary);
  background: var(--input-bg);
  transition: all 0.2s ease;
  line-height: 1.5;
  box-sizing: border-box;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04), inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.form-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(59, 130, 246, 0.15);
  background: var(--color-white);
}

.form-input:hover:not(:focus) {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06), inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.form-input:disabled {
  background: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.form-input::placeholder {
  color: var(--input-placeholder);
  font-weight: 400;
}

/* 增强输入框 */
.enhanced-form-input {
  width: 100%;
  padding: var(--input-padding-sm) var(--input-padding-md);
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.2s ease;
  line-height: 1.5;
  box-sizing: border-box;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.06), inset 0 1px 2px rgba(59, 130, 246, 0.02);
}

.enhanced-form-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(59, 130, 246, 0.15);
  background: var(--color-white);
}

.enhanced-form-input:hover:not(:focus) {
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.08), inset 0 1px 2px rgba(59, 130, 246, 0.02);
}

.enhanced-form-input:disabled {
  background: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.enhanced-form-input::placeholder {
  color: var(--input-placeholder);
  font-weight: 400;
}

/* 现代输入框 */
.form-input-modern {
  width: 100%;
  padding: var(--input-padding-md);
  border: 1px solid var(--color-primary-200);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.2s ease;
  line-height: 1.5;
  box-sizing: border-box;
}

.form-input-modern:focus {
  outline: none;
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-focus-blue);
  background: var(--color-white);
}

.form-input-modern:hover:not(:focus) {
  border-color: var(--color-primary-300);
}

/* 数字输入框 */
.form-input.numeric {
  text-align: center;
  font-weight: 600;
  color: var(--color-primary-700);
  border: none;
  background: transparent;
}

.enhanced-form-input.numeric {
  text-align: center;
  font-weight: 600;
  color: var(--color-primary-700);
  border: none;
  background: transparent;
}

/* 特殊输入框 - 查询输入 */
.query-input-textarea {
  transition: all 0.2s ease !important;
  background: transparent !important;
  border: none;
  resize: none;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-primary);
  width: 100%;
  min-height: 120px;
  padding: var(--space-3);
}

.query-input-textarea:focus {
  outline: none !important;
  background: transparent !important;
}

.query-input-textarea::placeholder {
  color: var(--input-placeholder);
  font-weight: 400;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📄 TEXTAREA - 文本域
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-textarea {
  width: 100%;
  padding: var(--input-padding-md);
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-primary);
  background: var(--input-bg);
  transition: all 0.2s ease;
  line-height: 1.6;
  min-height: 120px;
  resize: vertical;
  box-sizing: border-box;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--input-border-focus);
  box-shadow: var(--shadow-focus-blue);
  background: var(--color-white);
}

.form-textarea:hover:not(:focus) {
  border-color: var(--color-gray-400);
}

.form-textarea:disabled {
  background: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.form-textarea::placeholder {
  color: var(--input-placeholder);
  font-weight: 400;
}

.enhanced-form-textarea {
  width: 100%;
  padding: var(--input-padding-md);
  border: 1px solid var(--color-primary-200);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.2s ease;
  line-height: 1.6;
  min-height: 120px;
  resize: vertical;
  box-sizing: border-box;
}

.enhanced-form-textarea:focus {
  outline: none;
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-focus-blue);
  background: var(--color-white);
}

.enhanced-form-textarea:hover:not(:focus) {
  border-color: var(--color-primary-300);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📦 SELECT FIELDS - 选择字段
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-select {
  width: 100%;
  padding: var(--input-padding-sm) var(--input-padding-md);
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-primary);
  background: var(--input-bg);
  transition: all 0.2s ease;
  line-height: 1.5;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  box-sizing: border-box;
}

.form-select:focus {
  outline: none;
  border-color: var(--input-border-focus);
  box-shadow: var(--shadow-focus-blue);
  background-color: var(--color-white);
}

.form-select:hover:not(:focus) {
  border-color: var(--color-gray-400);
}

.form-select:disabled {
  background: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.enhanced-form-select {
  width: 100%;
  padding: var(--input-padding-sm) var(--input-padding-md);
  border: 1px solid var(--color-primary-200);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.2s ease;
  line-height: 1.5;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  box-sizing: border-box;
}

.enhanced-form-select:focus {
  outline: none;
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-focus-blue);
  background-color: var(--color-white);
}

.enhanced-form-select:hover:not(:focus) {
  border-color: var(--color-primary-300);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ☑️ CHECKBOXES & RADIOS - 复选框和单选框
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-checkbox,
.form-radio {
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-gray-300);
  background: var(--color-white);
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.form-checkbox {
  border-radius: 4px;
}

.form-radio {
  border-radius: 50%;
}

.form-checkbox:checked,
.form-radio:checked {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

.form-checkbox:checked::before {
  content: '✓';
  display: block;
  color: white;
  text-align: center;
  font-size: 12px;
  line-height: 12px;
  font-weight: bold;
}

.form-radio:checked::before {
  content: '';
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
  margin: 3px;
}

.form-checkbox:focus,
.form-radio:focus {
  outline: none;
  box-shadow: var(--shadow-focus-blue);
}

.form-checkbox:hover,
.form-radio:hover {
  border-color: var(--color-primary-400);
}

/* 复选框组合标签 */
.checkbox-group,
.radio-group {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  user-select: none;
}

.checkbox-group:hover .form-checkbox,
.radio-group:hover .form-radio {
  border-color: var(--color-primary-400);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎛️ TOGGLE SWITCHES - 切换开关
 * ═══════════════════════════════════════════════════════════════════════════ */

.toggle-wrapper {
  position: relative;
}

.toggle-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.toggle-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  background: var(--color-primary-50);
  border: 1px solid var(--color-primary-100);
  border-radius: 10px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

.toggle-label:hover {
  background: var(--color-primary-100);
  border-color: var(--color-primary-200);
}

.toggle-content {
  flex: 1;
  margin-right: var(--space-4);
}

.toggle-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.toggle-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.toggle-switch {
  position: relative;
  width: 44px;
  height: 24px;
  background: var(--color-gray-200);
  border-radius: 12px;
  transition: all 0.3s ease;
  flex-shrink: 0;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: var(--color-white);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.toggle-input:checked + .toggle-label .toggle-switch {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.toggle-input:checked + .toggle-label .toggle-slider {
  transform: translateX(20px);
  box-shadow: var(--shadow-md);
}

/* 增强切换开关 */
.enhanced-toggle-wrapper {
  position: relative;
}

.enhanced-toggle-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.enhanced-toggle-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  background: var(--color-primary-50);
  border: 1px solid var(--color-primary-100);
  border-radius: 10px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

.enhanced-toggle-label:hover {
  background: var(--color-primary-100);
  border-color: var(--color-primary-200);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔢 NUMERIC INPUTS - 数字输入框
 * ═══════════════════════════════════════════════════════════════════════════ */

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper.numeric {
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--color-primary-200);
  transition: all 0.3s ease;
}

.input-wrapper.numeric:hover {
  border-color: var(--color-primary-300);
}

.input-wrapper.numeric:focus-within {
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-focus-blue);
}

.input-wrapper.numeric.enhanced {
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--color-primary-200);
  transition: all 0.3s ease;
}

.input-wrapper.numeric.enhanced:hover {
  border-color: var(--color-primary-300);
}

.input-wrapper.numeric.enhanced:focus-within {
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-focus-blue);
}

.input-unit {
  position: absolute;
  right: var(--space-4);
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--color-gray-500);
  pointer-events: none;
  background: rgba(255, 255, 255, 0.9);
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
}

.input-status-indicator {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-success-500);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.input-wrapper:focus-within .input-status-indicator {
  opacity: 1;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 💬 FORM HINTS & ERRORS - 表单提示和错误
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-hint,
.field-hint {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--input-margin);
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-weight: 500;
}

.form-error {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--input-margin);
  font-size: 0.75rem;
  color: var(--color-error-600);
  font-weight: 500;
}

.form-success {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--input-margin);
  font-size: 0.75rem;
  color: var(--color-success-600);
  font-weight: 500;
}

.form-warning {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--input-margin);
  font-size: 0.75rem;
  color: var(--color-warning-600);
  font-weight: 500;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 FORM STATES - 表单状态
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 错误状态 */
.form-input.error,
.enhanced-form-input.error {
  border-color: var(--color-error-500);
  box-shadow: var(--shadow-focus-red);
}

.form-input.error:focus,
.enhanced-form-input.error:focus {
  border-color: var(--color-error-600);
  box-shadow: var(--shadow-focus-red);
}

/* 成功状态 */
.form-input.success,
.enhanced-form-input.success {
  border-color: var(--color-success-500);
  box-shadow: var(--shadow-focus-green);
}

.form-input.success:focus,
.enhanced-form-input.success:focus {
  border-color: var(--color-success-600);
  box-shadow: var(--shadow-focus-green);
}

/* 警告状态 */
.form-input.warning,
.enhanced-form-input.warning {
  border-color: var(--color-warning-500);
  box-shadow: var(--shadow-focus-amber);
}

.form-input.warning:focus,
.enhanced-form-input.warning:focus {
  border-color: var(--color-warning-600);
  box-shadow: var(--shadow-focus-amber);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📏 FORM LAYOUTS - 表单布局
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 网格布局 */
.form-grid {
  display: grid;
  gap: var(--grid-gap-md);
}

.form-grid-2 {
  display: grid;
  gap: var(--grid-gap-md);
  grid-template-columns: repeat(2, 1fr);
}

.form-grid-3 {
  display: grid;
  gap: var(--grid-gap-md);
  grid-template-columns: repeat(3, 1fr);
}

.form-grid-4 {
  display: grid;
  gap: var(--grid-gap-md);
  grid-template-columns: repeat(4, 1fr);
}

/* 数字网格 */
.numeric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: var(--grid-gap-md);
  margin-top: var(--space-4);
}

.numeric-grid.enhanced {
  gap: var(--grid-gap-lg);
}

/* 切换开关网格 */
.toggle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--grid-gap-md);
  margin-top: var(--space-4);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔧 SPECIAL FORM FIXES - 特殊表单修复
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 查询输入卡片修复 */
.query-input-card.optimized-card {
  background: rgba(255, 255, 255, 0.98) !important;
  border: 1px solid rgba(148, 163, 184, 0.15) !important;
  border-radius: 12px !important;
}

.query-input-card.optimized-card:hover {
  background: rgba(255, 255, 255, 1) !important;
  border-color: rgba(35, 146, 239, 0.25) !important;
}

/* 确保内部元素不会覆盖边框 */
.query-input-card > * {
  position: relative;
  z-index: 1;
}

/* 动画增强 */
.query-input-card,
.query-preview-card {
  will-change: transform, box-shadow, border-color;
}

/* 防止动画卡顿 */
.query-input-card *,
.query-preview-card * {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 1024px) {
  .numeric-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--grid-gap-sm);
  }
  
  .toggle-grid {
    grid-template-columns: 1fr;
    gap: var(--grid-gap-sm);
  }
  
  .form-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .form-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .numeric-grid {
    grid-template-columns: 1fr;
    gap: var(--grid-gap-xs);
  }
  
  .form-grid-2,
  .form-grid-3,
  .form-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .form-input,
  .enhanced-form-input {
    font-size: 16px; /* 防止iOS缩放 */
  }
  
  /* 移动端圆角适配 */
  .query-input-card,
  .query-preview-card {
    border-radius: 10px !important;
  }
  
  .query-input-card .rounded-lg,
  .query-input-card .rounded,
  .query-preview-card .rounded-lg,
  .query-preview-card .rounded {
    border-radius: 6px !important;
  }
}

/* 小屏幕hover效果调整 */
@media (hover: none) {
  .query-input-card:hover,
  .query-preview-card:hover {
    transform: none !important;
  }
  
  .form-label:hover,
  .enhanced-form-label:hover {
    color: var(--text-primary);
  }
  
  .form-input:hover,
  .enhanced-form-input:hover,
  .form-select:hover,
  .enhanced-form-select:hover {
    border-color: var(--input-border);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ♿ ACCESSIBILITY - 无障碍
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 焦点可见性 */
.form-input:focus-visible,
.enhanced-form-input:focus-visible,
.form-select:focus-visible,
.enhanced-form-select:focus-visible,
.form-textarea:focus-visible,
.enhanced-form-textarea:focus-visible,
.form-checkbox:focus-visible,
.form-radio:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .form-input,
  .enhanced-form-input,
  .form-select,
  .enhanced-form-select,
  .form-textarea,
  .enhanced-form-textarea,
  .toggle-switch,
  .toggle-slider,
  .input-wrapper,
  .form-label,
  .enhanced-form-label {
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .form-input,
  .enhanced-form-input,
  .form-select,
  .enhanced-form-select,
  .form-textarea,
  .enhanced-form-textarea {
    border-width: 2px;
  }
  
  .toggle-switch {
    border: 2px solid var(--color-gray-600);
  }
  
  .form-checkbox,
  .form-radio {
    border-width: 3px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .form-input,
  .enhanced-form-input,
  .form-select,
  .enhanced-form-select,
  .form-textarea,
  .enhanced-form-textarea {
    min-height: 44px;
  }

  .form-checkbox,
  .form-radio {
    min-width: 44px;
    min-height: 44px;
  }

  .toggle-switch {
    min-width: 44px;
    min-height: 44px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔄 MODE TOGGLE STYLES - 模式切换组件样式
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 模式切换容器 */
.mode-toggle-container {
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(148, 163, 184, 0.15);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(100, 116, 139, 0.06);
}

.mode-toggle-container:hover {
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.1);
}

/* 主要切换区域 */
.mode-toggle-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;
}

.mode-toggle-left {
  flex: 1;
  margin-right: 12px;
}

.mode-toggle-right {
  flex-shrink: 0;
}

/* 标签区域 */
.mode-toggle-label-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mode-toggle-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-gray-800);
  line-height: 1.4;
}

/* 描述文本 */
.mode-toggle-description {
  font-size: 12px;
  color: var(--color-gray-600);
  line-height: 1.5;
  margin-top: 4px;
  padding-left: 2px;
}

/* 功能说明列表 */
.mode-toggle-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.mode-toggle-feature-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: var(--color-gray-600);
  background: rgba(248, 250, 252, 0.8);
  padding: 2px 6px;
  border-radius: 4px;
}

/* Switch组件自定义样式 - 使用主题蓝色 */
.mode-toggle-switch {
  --semi-color-primary: #2392EF;
  --semi-color-primary-hover: #1F7ED6;
  --semi-color-primary-active: #1B6ABD;
  transition: all 0.2s ease;
}

.mode-toggle-switch:hover {
  transform: scale(1.05);
}

/* 简化版模式切换 */
.compact-mode-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(148, 163, 184, 0.12);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.compact-mode-toggle:hover {
  border-color: rgba(59, 130, 246, 0.2);
}

.compact-mode-toggle-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.compact-mode-toggle-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-gray-700);
  white-space: nowrap;
}

.compact-mode-toggle-switch {
  --semi-color-primary: #2392EF;
  --semi-color-primary-hover: #1F7ED6;
}

.compact-mode-toggle-loading {
  display: flex;
  align-items: center;
  color: var(--color-blue-500);
}

/* 去重开关样式 - 与底稿数据开关保持一致 */
.deduplication-switch {
  --semi-color-primary: #2392EF;
  --semi-color-primary-hover: #1F7ED6;
  --semi-color-primary-active: #1B6ABD;
  transition: all 0.2s ease;
}

.deduplication-switch:hover {
  transform: scale(1.05);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🚨 DATA SOURCE ERROR HANDLER STYLES - 数据源错误处理组件样式
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 错误处理容器 */
.data-source-error-handler {
  margin: 8px 0;
}

.error-container {
  border: 1px solid;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

/* 错误标题区域 */
.error-header {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
}

.error-icon-wrapper {
  flex-shrink: 0;
  margin-top: 2px;
}

.error-title-wrapper {
  flex: 1;
}

.error-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.error-description {
  font-size: 12px;
  color: var(--color-gray-600);
  margin: 0;
  line-height: 1.4;
}

/* 错误详情 */
.error-details {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  padding: 8px;
  margin: 8px 0;
  font-size: 11px;
}

.error-detail-item {
  display: flex;
  margin-bottom: 4px;
}

.error-detail-item:last-child {
  margin-bottom: 0;
}

.error-detail-label {
  font-weight: 500;
  color: var(--color-gray-700);
  margin-right: 8px;
  min-width: 60px;
}

.error-detail-value {
  color: var(--color-gray-600);
  word-break: break-word;
}

/* 错误操作按钮 */
.error-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.error-action-btn {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border: 1px solid;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.8);
}

.error-action-retry {
  border-color: var(--color-blue-300);
  color: var(--color-blue-700);
}

.error-action-retry:hover {
  background: var(--color-blue-50);
  border-color: var(--color-blue-400);
}

.error-action-fallback {
  border-color: var(--color-gray-300);
  color: var(--color-gray-700);
}

.error-action-fallback:hover {
  background: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

/* 简化版错误处理 */
.compact-error-handler {
  margin: 4px 0;
}

.compact-error-content {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border: 1px solid;
  border-radius: 4px;
  font-size: 11px;
}

.compact-error-text {
  flex: 1;
  font-weight: 500;
}

.compact-error-dismiss {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.compact-error-dismiss:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* 错误统计显示 */
.error-stats-display {
  background: rgba(255, 248, 220, 0.8);
  border: 1px solid rgba(245, 158, 11, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
}

.error-stats-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.error-stats-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--color-orange-700);
}

.error-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 8px;
}

.error-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.error-stat-label {
  font-size: 10px;
  color: var(--color-gray-600);
  margin-bottom: 2px;
}

.error-stat-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-orange-700);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🧪 INTERNAL DATA DEMO STYLES - 底稿数据演示组件样式
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 演示容器 */
.internal-data-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.demo-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 演示标题 */
.demo-header {
  margin-bottom: 24px;
  text-align: center;
}

.demo-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  color: var(--color-gray-800);
  margin-bottom: 8px;
}

.demo-description {
  font-size: 14px;
  color: var(--color-gray-600);
  line-height: 1.5;
  margin: 0;
}

/* 演示区块 */
.demo-section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.demo-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.demo-section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

/* 测试查询容器 */
.test-query-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-query-input {
  display: flex;
  gap: 8px;
  align-items: center;
}

.test-query-input .form-input {
  flex: 1;
}

/* 快速测试按钮 */
.quick-test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.quick-test-label {
  font-size: 12px;
  color: var(--color-gray-600);
  font-weight: 500;
  margin-right: 8px;
}

.quick-test-btn {
  padding: 4px 8px;
  font-size: 11px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 4px;
  color: var(--color-blue-700);
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-test-btn:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.quick-test-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 处理结果 */
.processing-result {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.15);
  border-radius: 8px;
  padding: 16px;
}

.result-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-bottom: 16px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-gray-600);
  min-width: 70px;
}

.result-value {
  font-size: 12px;
  color: var(--color-gray-800);
  word-break: break-word;
}

.result-content {
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  padding-top: 12px;
}

.result-content-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: 8px;
}

.result-content-text {
  font-size: 11px;
  color: var(--color-gray-600);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 4px;
  padding: 8px;
  max-height: 200px;
  overflow-y: auto;
  line-height: 1.4;
  white-space: pre-wrap;
}

/* 缓存统计 */
.cache-stats {
  background: rgba(240, 253, 244, 0.8);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 8px;
  padding: 12px;
}

.cache-stats-summary {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.cache-stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.cache-stat-label {
  font-size: 12px;
  color: var(--color-gray-600);
}

.cache-stat-value {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-green-700);
}

.cache-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .internal-data-demo {
    padding: 12px;
  }

  .demo-container {
    padding: 16px;
  }

  .test-query-input {
    flex-direction: column;
    align-items: stretch;
  }

  .quick-test-buttons {
    justify-content: flex-start;
  }

  .result-summary {
    grid-template-columns: 1fr;
  }

  .cache-stats-summary {
    flex-direction: column;
    gap: 8px;
  }
}